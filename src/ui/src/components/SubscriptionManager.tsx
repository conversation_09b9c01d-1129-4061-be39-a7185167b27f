import React, { useEffect, useState } from "react";
import { ApiService } from "../services/api";

interface SubscriptionManagerProps {
  onNodesUpdate: () => void;
}

interface Subscription {
  id: string;
  name: string;
  url: string;
  lastUpdate: number;
  nodeCount: number;
  isActive: boolean;
}

export const SubscriptionManager: React.FC<SubscriptionManagerProps> = ({ onNodesUpdate }) => {
  const [subscriptions, setSubscriptions] = useState<Subscription[]>([]);
  const [newSubscription, setNewSubscription] = useState({ name: "", url: "" });
  const [singleProxy, setSingleProxy] = useState("");
  const [batchUrls, setBatchUrls] = useState("");
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState("");

  // 加载订阅列表
  const loadSubscriptions = async () => {
    try {
      const response = await ApiService.getSubscriptions();
      if (response.success) {
        setSubscriptions(response.data || []);
      }
    } catch (error) {
      console.error("加载订阅列表失败:", error);
    }
  };

  useEffect(() => {
    loadSubscriptions();
  }, []);

  // 保存新订阅
  const handleSaveSubscription = async () => {
    if (!newSubscription.name || !newSubscription.url) {
      setMessage("请填写订阅名称和URL");
      return;
    }

    try {
      setLoading(true);
      const response = await ApiService.saveSubscription(newSubscription);
      if (response.success) {
        setMessage("订阅保存成功");
        setNewSubscription({ name: "", url: "" });
        loadSubscriptions();
      } else {
        setMessage(`保存失败: ${response.error}`);
      }
    } catch (error) {
      setMessage(`保存失败: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  // 解析订阅
  const handleParseSubscriptions = async () => {
    const urls = batchUrls
      .split("\n")
      .map((url) => url.trim())
      .filter((url) => url.length > 0);

    if (urls.length === 0 && !singleProxy) {
      setMessage("请输入订阅链接或单个代理链接");
      return;
    }

    try {
      setLoading(true);
      const response = await ApiService.parseSubscriptions({
        urls: urls.length > 0 ? urls : undefined,
        singleProxy: singleProxy || undefined,
      });

      if (response.success) {
        const { nodes, errors } = response.data;
        let msg = `成功解析 ${nodes.length} 个节点`;
        if (errors.length > 0) {
          msg += `，${errors.length} 个链接解析失败`;
        }
        setMessage(msg);
        setBatchUrls("");
        setSingleProxy("");
        onNodesUpdate();
      } else {
        setMessage(`解析失败: ${response.error}`);
      }
    } catch (error) {
      setMessage(`解析失败: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="subscription-manager">
      <div className="section">
        <h2>📡 订阅管理</h2>

        {/* 添加新订阅 */}
        <div className="card">
          <h3>添加订阅</h3>
          <div className="form-group">
            <label>订阅名称:</label>
            <input
              type="text"
              value={newSubscription.name}
              onChange={(e) => setNewSubscription({ ...newSubscription, name: e.target.value })}
              placeholder="输入订阅名称"
            />
          </div>
          <div className="form-group">
            <label>订阅URL:</label>
            <input
              type="url"
              value={newSubscription.url}
              onChange={(e) => setNewSubscription({ ...newSubscription, url: e.target.value })}
              placeholder="输入订阅链接"
            />
          </div>
          <button onClick={handleSaveSubscription} disabled={loading}>
            {loading ? "保存中..." : "保存订阅"}
          </button>
        </div>

        {/* 批量解析订阅 */}
        <div className="card">
          <h3>批量解析订阅</h3>
          <div className="form-group">
            <label>订阅链接 (每行一个):</label>
            <textarea
              value={batchUrls}
              onChange={(e) => setBatchUrls(e.target.value)}
              placeholder="输入订阅链接，每行一个"
              rows={5}
            />
          </div>
          <div className="form-group">
            <label>单个代理链接:</label>
            <input
              type="text"
              value={singleProxy}
              onChange={(e) => setSingleProxy(e.target.value)}
              placeholder="输入单个代理链接 (vmess://, vless://, trojan://, ss://)"
            />
          </div>
          <button onClick={handleParseSubscriptions} disabled={loading}>
            {loading ? "解析中..." : "开始解析"}
          </button>
        </div>

        {/* 已保存的订阅 */}
        <div className="card">
          <h3>已保存的订阅</h3>
          {subscriptions.length === 0 ? <p>暂无订阅</p> : (
            <div className="subscription-list">
              {subscriptions.map((sub) => (
                <div key={sub.id} className="subscription-item">
                  <div className="subscription-info">
                    <h4>{sub.name}</h4>
                    <p className="url">{sub.url}</p>
                    <div className="meta">
                      <span>节点数: {sub.nodeCount}</span>
                      <span>更新时间: {new Date(sub.lastUpdate).toLocaleString()}</span>
                      <span className={`status ${sub.isActive ? "active" : "inactive"}`}>
                        {sub.isActive ? "活跃" : "停用"}
                      </span>
                    </div>
                  </div>
                  <div className="subscription-actions">
                    <button
                      onClick={() => {
                        setBatchUrls(sub.url);
                        handleParseSubscriptions();
                      }}
                      disabled={loading}
                    >
                      重新解析
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* 消息提示 */}
        {message && (
          <div className={`message ${message.includes("失败") ? "error" : "success"}`}>
            {message}
          </div>
        )}
      </div>
    </div>
  );
};
