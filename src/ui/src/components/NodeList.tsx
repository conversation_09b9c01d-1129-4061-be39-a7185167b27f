import React, { useState } from "react";
import { ApiService } from "../services/api";

interface ProxyNode {
  id: string;
  name: string;
  type: string;
  server: string;
  port: number;
  testResult?: {
    connectivity: boolean;
    latency: number;
    downloadSpeed: number;
    uploadSpeed: number;
    timestamp: number;
    error?: string;
  };
}

interface NodeListProps {
  nodes: ProxyNode[];
  selectedNodes: string[];
  onNodeSelect: (nodeIds: string[]) => void;
  onNodesUpdate: () => void;
  loading: boolean;
}

export const NodeList: React.FC<NodeListProps> = ({
  nodes,
  selectedNodes,
  onNodeSelect,
  onNodesUpdate,
  loading,
}) => {
  const [filter, setFilter] = useState({
    protocol: "",
    keyword: "",
    connectivity: "",
  });
  const [testConfig, setTestConfig] = useState({
    timeout: 10,
    concurrency: 10,
    includeSpeed: true,
  });
  const [testing, setTesting] = useState(false);
  const [message, setMessage] = useState("");

  // 过滤节点
  const filteredNodes = nodes.filter((node) => {
    if (filter.protocol && node.type !== filter.protocol) return false;
    if (filter.keyword && !node.name.toLowerCase().includes(filter.keyword.toLowerCase())) {
      return false;
    }
    if (
      filter.connectivity === "connected" && (!node.testResult || !node.testResult.connectivity)
    ) return false;
    if (filter.connectivity === "disconnected" && node.testResult?.connectivity) return false;
    return true;
  });

  // 选择/取消选择节点
  const handleNodeToggle = (nodeId: string) => {
    const newSelected = selectedNodes.includes(nodeId)
      ? selectedNodes.filter((id) => id !== nodeId)
      : [...selectedNodes, nodeId];
    onNodeSelect(newSelected);
  };

  // 全选/取消全选
  const handleSelectAll = () => {
    if (selectedNodes.length === filteredNodes.length) {
      onNodeSelect([]);
    } else {
      onNodeSelect(filteredNodes.map((node) => node.id));
    }
  };

  // 测试节点
  const handleTestNodes = async () => {
    const nodesToTest = selectedNodes.length > 0 ? selectedNodes : filteredNodes.map((n) => n.id);

    if (nodesToTest.length === 0) {
      setMessage("没有可测试的节点");
      return;
    }

    try {
      setTesting(true);
      setMessage(`开始测试 ${nodesToTest.length} 个节点...`);

      const response = await ApiService.testNodes({
        nodeIds: nodesToTest,
        includeSpeed: testConfig.includeSpeed,
        config: {
          timeout: testConfig.timeout,
          concurrency: testConfig.concurrency,
          testUrl: "http://www.gstatic.com/generate_204",
          speedTestSize: 1024,
        },
      });

      if (response.success) {
        setMessage(`测试完成: ${response.data.length} 个节点`);
        onNodesUpdate();
      } else {
        setMessage(`测试失败: ${response.error}`);
      }
    } catch (error) {
      setMessage(`测试失败: ${error}`);
    } finally {
      setTesting(false);
    }
  };

  // 格式化延迟
  const formatLatency = (latency: number) => {
    if (latency < 0) return "超时";
    return `${latency}ms`;
  };

  // 格式化速度
  const formatSpeed = (speed: number) => {
    if (speed < 0) return "未测试";
    if (speed < 1024) return `${speed}KB/s`;
    return `${(speed / 1024).toFixed(1)}MB/s`;
  };

  // 获取连通性状态
  const getConnectivityStatus = (node: ProxyNode) => {
    if (!node.testResult) return { text: "未测试", className: "untested" };
    if (node.testResult.connectivity) return { text: "连通", className: "connected" };
    return { text: "断开", className: "disconnected" };
  };

  return (
    <div className="node-list">
      <div className="section">
        <h2>🌐 节点列表</h2>

        {/* 过滤器 */}
        <div className="card">
          <h3>筛选条件</h3>
          <div className="filters">
            <div className="filter-group">
              <label>协议类型:</label>
              <select
                value={filter.protocol}
                onChange={(e) => setFilter({ ...filter, protocol: e.target.value })}
              >
                <option value="">全部</option>
                <option value="vmess">VMess</option>
                <option value="vless">VLESS</option>
                <option value="trojan">Trojan</option>
                <option value="ss">Shadowsocks</option>
              </select>
            </div>
            <div className="filter-group">
              <label>关键词:</label>
              <input
                type="text"
                value={filter.keyword}
                onChange={(e) => setFilter({ ...filter, keyword: e.target.value })}
                placeholder="搜索节点名称"
              />
            </div>
            <div className="filter-group">
              <label>连通性:</label>
              <select
                value={filter.connectivity}
                onChange={(e) => setFilter({ ...filter, connectivity: e.target.value })}
              >
                <option value="">全部</option>
                <option value="connected">已连通</option>
                <option value="disconnected">已断开</option>
              </select>
            </div>
          </div>
        </div>

        {/* 测试配置 */}
        <div className="card">
          <h3>测试配置</h3>
          <div className="test-config">
            <div className="config-group">
              <label>超时时间 (秒):</label>
              <input
                type="number"
                value={testConfig.timeout}
                onChange={(e) =>
                  setTestConfig({ ...testConfig, timeout: parseInt(e.target.value) })}
                min="1"
                max="60"
              />
            </div>
            <div className="config-group">
              <label>并发数:</label>
              <input
                type="number"
                value={testConfig.concurrency}
                onChange={(e) =>
                  setTestConfig({ ...testConfig, concurrency: parseInt(e.target.value) })}
                min="1"
                max="50"
              />
            </div>
            <div className="config-group">
              <label>
                <input
                  type="checkbox"
                  checked={testConfig.includeSpeed}
                  onChange={(e) => setTestConfig({ ...testConfig, includeSpeed: e.target.checked })}
                />
                包含速度测试
              </label>
            </div>
          </div>
          <div className="test-actions">
            <button onClick={handleTestNodes} disabled={testing || loading}>
              {testing ? "测试中..." : "开始测试"}
            </button>
            <span className="test-info">
              {selectedNodes.length > 0
                ? `将测试选中的 ${selectedNodes.length} 个节点`
                : `将测试所有 ${filteredNodes.length} 个节点`}
            </span>
          </div>
        </div>

        {/* 节点操作 */}
        <div className="card">
          <div className="node-actions">
            <button onClick={handleSelectAll}>
              {selectedNodes.length === filteredNodes.length ? "取消全选" : "全选"}
            </button>
            <span className="node-count">
              显示 {filteredNodes.length} / {nodes.length} 个节点
              {selectedNodes.length > 0 && ` (已选择 ${selectedNodes.length} 个)`}
            </span>
          </div>
        </div>

        {/* 节点表格 */}
        <div className="card">
          {loading
            ? <div className="loading">加载中...</div>
            : filteredNodes.length === 0
            ? <div className="empty">没有找到节点</div>
            : (
              <div className="node-table">
                <table>
                  <thead>
                    <tr>
                      <th>选择</th>
                      <th>名称</th>
                      <th>类型</th>
                      <th>服务器</th>
                      <th>端口</th>
                      <th>连通性</th>
                      <th>延迟</th>
                      <th>下载速度</th>
                      <th>上传速度</th>
                      <th>测试时间</th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredNodes.map((node) => {
                      const status = getConnectivityStatus(node);
                      return (
                        <tr
                          key={node.id}
                          className={selectedNodes.includes(node.id) ? "selected" : ""}
                        >
                          <td>
                            <input
                              type="checkbox"
                              checked={selectedNodes.includes(node.id)}
                              onChange={() => handleNodeToggle(node.id)}
                            />
                          </td>
                          <td className="node-name" title={node.name}>{node.name}</td>
                          <td className={`node-type ${node.type}`}>{node.type.toUpperCase()}</td>
                          <td>{node.server}</td>
                          <td>{node.port}</td>
                          <td className={`connectivity ${status.className}`}>{status.text}</td>
                          <td>{node.testResult ? formatLatency(node.testResult.latency) : "-"}</td>
                          <td>
                            {node.testResult ? formatSpeed(node.testResult.downloadSpeed) : "-"}
                          </td>
                          <td>
                            {node.testResult ? formatSpeed(node.testResult.uploadSpeed) : "-"}
                          </td>
                          <td>
                            {node.testResult
                              ? new Date(node.testResult.timestamp).toLocaleString()
                              : "-"}
                          </td>
                        </tr>
                      );
                    })}
                  </tbody>
                </table>
              </div>
            )}
        </div>

        {/* 消息提示 */}
        {message && (
          <div className={`message ${message.includes("失败") ? "error" : "success"}`}>
            {message}
          </div>
        )}
      </div>
    </div>
  );
};
