// API服务类

interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

interface ProxyNode {
  id: string;
  name: string;
  type: string;
  server: string;
  port: number;
  testResult?: {
    connectivity: boolean;
    latency: number;
    downloadSpeed: number;
    uploadSpeed: number;
    timestamp: number;
    error?: string;
  };
}

interface TestConfig {
  timeout: number;
  concurrency: number;
  testUrl: string;
  speedTestSize: number;
}

interface Subscription {
  id: string;
  name: string;
  url: string;
  lastUpdate: number;
  nodeCount: number;
  isActive: boolean;
}

export class ApiService {
  private static baseUrl = "/api";

  private static async request<T>(
    endpoint: string,
    options: RequestInit = {},
  ): Promise<ApiResponse<T>> {
    try {
      const response = await fetch(`${this.baseUrl}${endpoint}`, {
        headers: {
          "Content-Type": "application/json",
          ...options.headers,
        },
        ...options,
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error("API请求失败:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "未知错误",
      };
    }
  }

  // 解析订阅
  static async parseSubscriptions(data: {
    urls?: string[];
    singleProxy?: string;
  }): Promise<ApiResponse<{ nodes: ProxyNode[]; errors: Array<{ url: string; error: string }> }>> {
    return this.request("/subscriptions/parse", {
      method: "POST",
      body: JSON.stringify(data),
    });
  }

  // 获取节点列表
  static async getNodes(filters?: {
    protocols?: string[];
    keywords?: string[];
  }): Promise<ApiResponse<ProxyNode[]>> {
    const params = new URLSearchParams();
    if (filters?.protocols) {
      params.set("protocols", filters.protocols.join(","));
    }
    if (filters?.keywords) {
      params.set("keywords", filters.keywords.join(","));
    }

    const query = params.toString();
    return this.request(`/nodes${query ? `?${query}` : ""}`);
  }

  // 测试节点
  static async testNodes(data: {
    nodeIds?: string[];
    includeSpeed?: boolean;
    config?: Partial<TestConfig>;
  }): Promise<ApiResponse<any[]>> {
    return this.request("/nodes/test", {
      method: "POST",
      body: JSON.stringify(data),
    });
  }

  // 生成配置文件
  static async generateConfig(data: {
    templatePath: string;
    nodeIds?: string[];
    outputPath?: string;
    options?: {
      groupName?: string;
      insertToGroups?: boolean;
      backupOriginal?: boolean;
    };
  }): Promise<ApiResponse<{ config: any; validation: any }>> {
    return this.request("/config/generate", {
      method: "POST",
      body: JSON.stringify(data),
    });
  }

  // 获取测试历史
  static async getTestResults(nodeIds?: string[]): Promise<ApiResponse<any[]>> {
    const params = new URLSearchParams();
    if (nodeIds) {
      params.set("nodeIds", nodeIds.join(","));
    }

    const query = params.toString();
    return this.request(`/test-results${query ? `?${query}` : ""}`);
  }

  // 获取订阅列表
  static async getSubscriptions(): Promise<ApiResponse<Subscription[]>> {
    return this.request("/subscriptions");
  }

  // 保存订阅
  static async saveSubscription(data: {
    name: string;
    url: string;
  }): Promise<ApiResponse<Subscription>> {
    return this.request("/subscriptions", {
      method: "POST",
      body: JSON.stringify(data),
    });
  }

  // 清理数据
  static async cleanup(data: {
    daysToKeep?: number;
  }): Promise<ApiResponse<void>> {
    return this.request("/cleanup", {
      method: "POST",
      body: JSON.stringify(data),
    });
  }

  // 健康检查
  static async healthCheck(): Promise<ApiResponse<{ status: string; timestamp: number }>> {
    return this.request("/health");
  }
}
