/* 全局样式 */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  font-family:
    -apple-system,
    BlinkMacSystemFont,
    "Segoe UI",
    "Roboto",
    "Oxygen",
    "Ubuntu",
    "Cantarell",
    "Fira Sans",
    "Droid Sans",
    "Helvetica Neue",
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f5f5f5;
}

/* 应用布局 */
.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.app-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 2rem;
  text-align: center;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.app-header h1 {
  margin: 0 0 0.5rem 0;
  font-size: 2.5rem;
  font-weight: 700;
}

.app-header p {
  margin: 0;
  font-size: 1.1rem;
  opacity: 0.9;
}

/* 导航栏 */
.app-nav {
  background: white;
  padding: 0;
  display: flex;
  border-bottom: 1px solid #e0e0e0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.app-nav button {
  flex: 1;
  padding: 1rem 2rem;
  border: none;
  background: white;
  color: #666;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  border-bottom: 3px solid transparent;
}

.app-nav button:hover {
  background: #f8f9fa;
  color: #333;
}

.app-nav button.active {
  color: #667eea;
  border-bottom-color: #667eea;
  background: #f8f9ff;
}

/* 主内容区 */
.app-main {
  flex: 1;
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
  width: 100%;
}

/* 卡片样式 */
.card {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e0e0e0;
}

.card h3 {
  margin: 0 0 1rem 0;
  color: #333;
  font-size: 1.2rem;
  font-weight: 600;
}

/* 表单样式 */
.form-group {
  margin-bottom: 1rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #555;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
}

.form-group textarea {
  resize: vertical;
  min-height: 100px;
}

/* 按钮样式 */
button {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 4px;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
}

button.primary {
  background: #667eea;
  color: white;
}

button.primary:hover:not(:disabled) {
  background: #5a6fd8;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

button.secondary {
  background: #6c757d;
  color: white;
}

button.secondary:hover:not(:disabled) {
  background: #5a6268;
}

button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: none !important;
}

/* 消息提示 */
.message {
  padding: 1rem;
  border-radius: 4px;
  margin: 1rem 0;
  font-weight: 500;
}

.message.success {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.message.error {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

/* 加载和空状态 */
.loading,
.empty {
  text-align: center;
  padding: 2rem;
  color: #666;
  font-style: italic;
}

/* 过滤器样式 */
.filters {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 1rem;
}

.filter-group {
  display: flex;
  flex-direction: column;
}

.filter-group label {
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #555;
}

.filter-actions {
  margin-top: 1rem;
  display: flex;
  gap: 1rem;
  align-items: center;
}

/* 表格样式 */
.node-table,
.results-table {
  overflow-x: auto;
}

.node-table table,
.results-table table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 1rem;
}

.node-table th,
.node-table td,
.results-table th,
.results-table td {
  padding: 0.75rem;
  text-align: left;
  border-bottom: 1px solid #e0e0e0;
}

.node-table th,
.results-table th {
  background: #f8f9fa;
  font-weight: 600;
  color: #555;
  position: sticky;
  top: 0;
  z-index: 1;
}

.node-table th.sortable,
.results-table th.sortable {
  cursor: pointer;
  user-select: none;
  transition: background-color 0.3s ease;
}

.node-table th.sortable:hover,
.results-table th.sortable:hover {
  background: #e9ecef;
}

.node-table tr:hover,
.results-table tr:hover {
  background: #f8f9ff;
}

.node-table tr.selected {
  background: #e3f2fd;
}

/* 节点类型样式 */
.node-type {
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
}

.node-type.vmess {
  background: #e3f2fd;
  color: #1976d2;
}

.node-type.vless {
  background: #f3e5f5;
  color: #7b1fa2;
}

.node-type.trojan {
  background: #fff3e0;
  color: #f57c00;
}

.node-type.ss {
  background: #e8f5e8;
  color: #388e3c;
}

/* 连通性状态 */
.connectivity {
  font-weight: 600;
}

.connectivity.connected {
  color: #4caf50;
}

.connectivity.disconnected {
  color: #f44336;
}

.connectivity.untested {
  color: #9e9e9e;
}

/* 性能指标颜色 */
.good {
  color: #4caf50;
}

.medium {
  color: #ff9800;
}

.poor {
  color: #f44336;
}

/* 节点操作 */
.node-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.node-count {
  color: #666;
  font-size: 0.9rem;
}

/* 订阅管理样式 */
.subscription-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.subscription-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  background: #fafafa;
}

.subscription-info h4 {
  margin: 0 0 0.5rem 0;
  color: #333;
}

.subscription-info .url {
  color: #666;
  font-size: 0.9rem;
  word-break: break-all;
  margin: 0 0 0.5rem 0;
}

.subscription-info .meta {
  display: flex;
  gap: 1rem;
  font-size: 0.8rem;
  color: #888;
}

.subscription-info .status.active {
  color: #4caf50;
}

.subscription-info .status.inactive {
  color: #f44336;
}

/* 测试配置样式 */
.test-config {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 1rem;
}

.config-group {
  display: flex;
  flex-direction: column;
}

.test-actions {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.test-info {
  color: #666;
  font-size: 0.9rem;
}

/* 统计信息样式 */
.stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 4px;
}

.stat-label {
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 0.5rem;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: 600;
  color: #333;
}

.stat-value.connected {
  color: #4caf50;
}

/* 配置生成样式 */
.node-preview {
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 1rem;
  background: #fafafa;
}

.preview-stats {
  margin-bottom: 1rem;
  font-weight: 500;
  color: #555;
}

.preview-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  max-height: 200px;
  overflow-y: auto;
}

.preview-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  background: white;
  border-radius: 4px;
  border: 1px solid #e0e0e0;
}

.preview-more {
  text-align: center;
  color: #666;
  font-style: italic;
  padding: 0.5rem;
}

.protocol-checkboxes {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.protocol-checkboxes label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0;
}

.generate-actions {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.config-preview {
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 1rem;
  background: #f8f9fa;
}

.config-info {
  margin-bottom: 1rem;
}

.config-info p {
  margin: 0.5rem 0;
}

.config-content {
  background: #2d3748;
  color: #e2e8f0;
  padding: 1rem;
  border-radius: 4px;
  overflow-x: auto;
  font-family: "Courier New", monospace;
  font-size: 0.9rem;
  max-height: 400px;
  overflow-y: auto;
}

/* 错误信息 */
.error-info {
  margin-left: 0.5rem;
  cursor: help;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .app-nav {
    flex-direction: column;
  }

  .app-nav button {
    border-bottom: 1px solid #e0e0e0;
    border-right: none;
  }

  .app-nav button.active {
    border-bottom-color: #e0e0e0;
    border-left: 3px solid #667eea;
  }

  .app-main {
    padding: 1rem;
  }

  .filters {
    grid-template-columns: 1fr;
  }

  .stats {
    grid-template-columns: repeat(2, 1fr);
  }

  .test-config {
    grid-template-columns: 1fr;
  }

  .subscription-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .node-actions {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .generate-actions {
    flex-direction: column;
    align-items: stretch;
  }
}
