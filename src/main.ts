#!/usr/bin/env -S deno run --allow-all

import { ApiServer } from "./api/server.ts";
import { logger } from "./utils/logger.ts";
import { parseArgs } from "@std/cli/parse_args";

/**
 * 主应用程序
 */
class SubNodeManage {
  private apiServer: ApiServer;

  constructor(port: number) {
    this.apiServer = new ApiServer(port);
  }

  /**
   * 启动应用
   */
  async start(): Promise<void> {
    try {
      logger.info("Sub Node Manage 启动中...", "Main");

      // 启动API服务器
      await this.apiServer.start();
    } catch (error) {
      logger.error(`应用启动失败: ${error.message}`, "Main");
      Deno.exit(1);
    }
  }

  /**
   * 停止应用
   */
  async stop(): Promise<void> {
    try {
      logger.info("Sub Node Manage 停止中...", "Main");

      await this.apiServer.stop();

      logger.info("Sub Node Manage 已停止", "Main");
    } catch (error) {
      logger.error(`应用停止失败: ${error.message}`, "Main");
    }
  }
}

/**
 * 显示帮助信息
 */
function showHelp() {
  console.log(`
Sub Node Manage - 基于mihomo内核的代理订阅管理工具

用法:
  deno run --allow-all src/main.ts [选项]

选项:
  -p, --port <port>     API服务器端口 (默认: 8080)
  -h, --help           显示帮助信息
  -v, --version        显示版本信息

示例:
  deno run --allow-all src/main.ts --port 3000
  deno task dev
  deno task build

更多信息请访问: https://github.com/your-repo/sub-node-manage
  `);
}

/**
 * 显示版本信息
 */
function showVersion() {
  console.log("Sub Node Manage v1.0.0");
}

/**
 * 主函数
 */
async function main() {
  const args = parseArgs(Deno.args, {
    string: ["port", "p"],
    boolean: ["help", "version", "h", "v"],
    alias: {
      p: "port",
      h: "help",
      v: "version",
    },
    default: {
      port: "8080",
    },
  });

  // 显示帮助信息
  if (args.help) {
    showHelp();
    return;
  }

  // 显示版本信息
  if (args.version) {
    showVersion();
    return;
  }

  // 解析端口号
  const port = parseInt(args.port, 10);
  if (isNaN(port) || port < 1 || port > 65535) {
    console.error("错误: 无效的端口号");
    Deno.exit(1);
  }

  // 创建应用实例
  const app = new SubNodeManage(port);

  // 处理退出信号
  const handleExit = async (signal: string) => {
    logger.info(`收到 ${signal} 信号，正在关闭应用...`, "Main");
    await app.stop();
    Deno.exit(0);
  };

  // 注册信号处理器
  Deno.addSignalListener("SIGINT", () => handleExit("SIGINT"));
  Deno.addSignalListener("SIGTERM", () => handleExit("SIGTERM"));

  // 启动应用
  try {
    await app.start();
  } catch (error) {
    logger.error(`应用运行失败: ${error.message}`, "Main");
    Deno.exit(1);
  }
}

// 运行主函数
if (import.meta.main) {
  main().catch((error) => {
    console.error("未处理的错误:", error);
    Deno.exit(1);
  });
}
