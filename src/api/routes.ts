import { Router } from "@oak/oak";
import { ApiResponse, ProxyNode, Subscription, TestConfig, TestResult } from "../types/index.ts";
import { SubscriptionParser } from "../parsers/subscription.ts";
import { DEFAULT_TEST_CONFIG, NodeTester } from "../testers/index.ts";
import { DatabaseManager } from "../storage/database.ts";
import { MihomoConfigGenerator } from "../config/mihomo.ts";
import { logger } from "../utils/logger.ts";
import { generateUUID } from "../utils/crypto.ts";

/**
 * API路由器
 */
export class ApiRouter {
  private router: Router;
  private subscriptionParser: SubscriptionParser;
  private nodeTester: NodeTester;
  private database: DatabaseManager;
  private configGenerator: MihomoConfigGenerator;

  constructor() {
    this.router = new Router();
    this.subscriptionParser = new SubscriptionParser();
    this.nodeTester = new NodeTester(DEFAULT_TEST_CONFIG);
    this.database = new DatabaseManager();
    this.configGenerator = new MihomoConfigGenerator();

    this.setupRoutes();
  }

  private setupRoutes() {
    // 健康检查
    this.router.get("/api/health", (ctx) => {
      ctx.response.body = { status: "ok", timestamp: Date.now() };
    });

    // 订阅管理
    this.router.post("/api/subscriptions/parse", async (ctx) => {
      try {
        const body = await ctx.request.body.json();
        const { urls, singleProxy } = body;

        let nodes: ProxyNode[] = [];
        const errors: Array<{ url: string; error: string }> = [];

        // 解析订阅链接
        if (urls && Array.isArray(urls)) {
          const result = await this.subscriptionParser.parseMultipleSubscriptions(urls);
          nodes.push(...result.nodes);
          errors.push(...result.errors);
        }

        // 解析单个代理链接
        if (singleProxy) {
          const node = this.subscriptionParser.parseSingleProxy(singleProxy);
          if (node) {
            nodes.push(node);
          } else {
            errors.push({ url: singleProxy, error: "解析失败" });
          }
        }

        // 保存到数据库
        if (nodes.length > 0) {
          this.database.saveNodes(nodes);
        }

        const response: ApiResponse = {
          success: true,
          data: { nodes, errors },
          message: `成功解析 ${nodes.length} 个节点`,
        };

        ctx.response.body = response;
      } catch (error) {
        logger.error("解析订阅失败", "ApiRouter", error);
        ctx.response.body = {
          success: false,
          error: error.message,
        };
      }
    });

    // 获取节点列表
    this.router.get("/api/nodes", async (ctx) => {
      try {
        const url = new URL(ctx.request.url);
        const protocols = url.searchParams.get("protocols")?.split(",");
        const keywords = url.searchParams.get("keywords")?.split(",");

        const filters = {
          protocols: protocols?.filter((p) => p.trim()),
          keywords: keywords?.filter((k) => k.trim()),
        };

        const nodes = this.database.getNodes(filters);
        const testResults = this.database.getLatestTestResults(nodes.map((n) => n.id));

        // 合并节点和测试结果
        const nodesWithResults = nodes.map((node) => ({
          ...node,
          testResult: testResults.get(node.id),
        }));

        ctx.response.body = {
          success: true,
          data: nodesWithResults,
        };
      } catch (error) {
        logger.error("获取节点列表失败", "ApiRouter", error);
        ctx.response.body = {
          success: false,
          error: error.message,
        };
      }
    });

    // 测试节点
    this.router.post("/api/nodes/test", async (ctx) => {
      try {
        const body = await ctx.request.body.json();
        const { nodeIds, includeSpeed = true, config } = body;

        // 更新测试配置
        if (config) {
          this.nodeTester.updateConfig(config);
        }

        // 获取要测试的节点
        const allNodes = this.database.getNodes();
        const nodesToTest = nodeIds
          ? allNodes.filter((node) => nodeIds.includes(node.id))
          : allNodes;

        if (nodesToTest.length === 0) {
          ctx.response.body = {
            success: false,
            error: "没有找到要测试的节点",
          };
          return;
        }

        // 开始测试
        const results = await this.nodeTester.testNodes(nodesToTest, {
          includeSpeed,
          onProgress: (completed, total, current) => {
            logger.info(`测试进度: ${completed}/${total}`, "ApiRouter");
          },
        });

        // 保存测试结果
        this.database.saveTestResults(results);

        ctx.response.body = {
          success: true,
          data: results,
          message: `完成 ${results.length} 个节点的测试`,
        };
      } catch (error) {
        logger.error("测试节点失败", "ApiRouter", error);
        ctx.response.body = {
          success: false,
          error: error.message,
        };
      }
    });

    // 生成配置文件
    this.router.post("/api/config/generate", async (ctx) => {
      try {
        const body = await ctx.request.body().value;
        const { templatePath, nodeIds, outputPath, options = {} } = body;

        // 读取模板
        const template = await this.configGenerator.readTemplate(templatePath);

        // 获取选中的节点
        const allNodes = this.database.getNodes();
        const selectedNodes = nodeIds
          ? allNodes.filter((node) => nodeIds.includes(node.id))
          : allNodes;

        if (selectedNodes.length === 0) {
          ctx.response.body = {
            success: false,
            error: "没有选中任何节点",
          };
          return;
        }

        // 生成配置
        const config = this.configGenerator.generateConfig(template, selectedNodes, options);

        // 验证配置
        const validation = this.configGenerator.validateConfig(config);
        if (!validation.valid) {
          ctx.response.body = {
            success: false,
            error: "生成的配置文件格式错误",
            data: validation.errors,
          };
          return;
        }

        // 保存配置文件
        if (outputPath) {
          // 备份原文件（如果存在）
          try {
            await Deno.stat(outputPath);
            await this.configGenerator.backupConfig(outputPath);
          } catch {
            // 文件不存在，无需备份
          }

          await this.configGenerator.saveConfig(config, outputPath);
        }

        ctx.response.body = {
          success: true,
          data: { config, validation },
          message: `成功生成包含 ${selectedNodes.length} 个节点的配置文件`,
        };
      } catch (error) {
        logger.error("生成配置文件失败", "ApiRouter", error);
        ctx.response.body = {
          success: false,
          error: error.message,
        };
      }
    });

    // 获取测试历史
    this.router.get("/api/test-results", async (ctx) => {
      try {
        const url = new URL(ctx.request.url);
        const nodeIds = url.searchParams.get("nodeIds")?.split(",");

        const results = this.database.getLatestTestResults(nodeIds);

        ctx.response.body = {
          success: true,
          data: Array.from(results.values()),
        };
      } catch (error) {
        logger.error("获取测试历史失败", "ApiRouter", error);
        ctx.response.body = {
          success: false,
          error: error.message,
        };
      }
    });

    // 获取订阅列表
    this.router.get("/api/subscriptions", async (ctx) => {
      try {
        const subscriptions = this.database.getSubscriptions();
        ctx.response.body = {
          success: true,
          data: subscriptions,
        };
      } catch (error) {
        logger.error("获取订阅列表失败", "ApiRouter", error);
        ctx.response.body = {
          success: false,
          error: error.message,
        };
      }
    });

    // 保存订阅
    this.router.post("/api/subscriptions", async (ctx) => {
      try {
        const body = await ctx.request.body().value;
        const { name, url } = body;

        const subscription: Subscription = {
          id: generateUUID(),
          name,
          url,
          lastUpdate: Date.now(),
          nodeCount: 0,
          isActive: true,
        };

        this.database.saveSubscription(subscription);

        ctx.response.body = {
          success: true,
          data: subscription,
          message: "订阅保存成功",
        };
      } catch (error) {
        logger.error("保存订阅失败", "ApiRouter", error);
        ctx.response.body = {
          success: false,
          error: error.message,
        };
      }
    });

    // 清理数据
    this.router.post("/api/cleanup", async (ctx) => {
      try {
        const body = await ctx.request.body().value;
        const { daysToKeep = 30 } = body;

        this.database.cleanupOldResults(daysToKeep);

        ctx.response.body = {
          success: true,
          message: `清理了 ${daysToKeep} 天前的测试结果`,
        };
      } catch (error) {
        logger.error("清理数据失败", "ApiRouter", error);
        ctx.response.body = {
          success: false,
          error: error.message,
        };
      }
    });
  }

  getRouter(): Router {
    return this.router;
  }

  /**
   * 关闭资源
   */
  close() {
    this.database.close();
  }
}
