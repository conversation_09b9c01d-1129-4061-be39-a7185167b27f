import { Application, Context, Next } from "@oak/oak";
import { oakCors } from "https://deno.land/x/cors@v1.2.2/mod.ts";
import { ApiRouter } from "./routes.ts";
import { logger } from "../utils/logger.ts";

/**
 * API服务器
 */
export class ApiServer {
  private app: Application;
  private apiRouter: ApiRouter;
  private port: number;

  constructor(port = 8080) {
    this.port = port;
    this.app = new Application();
    this.apiRouter = new ApiRouter();

    this.setupMiddleware();
    this.setupRoutes();
  }

  private setupMiddleware() {
    // CORS支持
    this.app.use(oakCors({
      origin: true,
      credentials: true,
    }));

    // 请求日志
    this.app.use(async (ctx: Context, next: Next) => {
      const start = Date.now();
      await next();
      const ms = Date.now() - start;

      logger.info(
        `${ctx.request.method} ${ctx.request.url.pathname} - ${ctx.response.status} - ${ms}ms`,
        "ApiServer",
      );
    });

    // 错误处理
    this.app.use(async (ctx: Context, next: Next) => {
      try {
        await next();
      } catch (error) {
        logger.error(`请求处理错误: ${error.message}`, "ApiServer", {
          method: ctx.request.method,
          url: ctx.request.url.pathname,
          error: error.stack,
        });

        ctx.response.status = 500;
        ctx.response.body = {
          success: false,
          error: "内部服务器错误",
          message: error.message,
        };
      }
    });


  }

  private setupRoutes() {
    // API路由
    this.app.use(this.apiRouter.getRouter().routes());
    this.app.use(this.apiRouter.getRouter().allowedMethods());

    // 静态文件服务（前端）
    this.app.use(async (ctx: Context, next: Next) => {
      if (ctx.request.url.pathname.startsWith("/api/")) {
        await next();
        return;
      }

      try {
        // 尝试提供静态文件
        const filePath = ctx.request.url.pathname === "/"
          ? "/index.html"
          : ctx.request.url.pathname;

        const fullPath = `src/ui/dist${filePath}`;

        try {
          const file = await Deno.readFile(fullPath);
          const ext = filePath.split(".").pop()?.toLowerCase();

          // 设置Content-Type
          switch (ext) {
            case "html":
              ctx.response.headers.set("Content-Type", "text/html");
              break;
            case "js":
              ctx.response.headers.set("Content-Type", "application/javascript");
              break;
            case "css":
              ctx.response.headers.set("Content-Type", "text/css");
              break;
            case "json":
              ctx.response.headers.set("Content-Type", "application/json");
              break;
            case "png":
              ctx.response.headers.set("Content-Type", "image/png");
              break;
            case "jpg":
            case "jpeg":
              ctx.response.headers.set("Content-Type", "image/jpeg");
              break;
            case "svg":
              ctx.response.headers.set("Content-Type", "image/svg+xml");
              break;
            case "ico":
              ctx.response.headers.set("Content-Type", "image/x-icon");
              break;
            default:
              ctx.response.headers.set("Content-Type", "application/octet-stream");
          }

          ctx.response.body = file;
        } catch {
          // 文件不存在，返回index.html（SPA路由）
          if (!filePath.includes(".")) {
            try {
              const indexFile = await Deno.readFile("src/ui/dist/index.html");
              ctx.response.headers.set("Content-Type", "text/html");
              ctx.response.body = indexFile;
            } catch {
              ctx.response.status = 404;
              ctx.response.body = "页面未找到";
            }
          } else {
            ctx.response.status = 404;
            ctx.response.body = "文件未找到";
          }
        }
      } catch (error) {
        logger.error(`静态文件服务错误: ${error.message}`, "ApiServer");
        ctx.response.status = 500;
        ctx.response.body = "服务器错误";
      }
    });
  }

  /**
   * 启动服务器
   */
  async start(): Promise<void> {
    try {
      logger.info(`API服务器启动中，端口: ${this.port}`, "ApiServer");

      await this.app.listen({
        port: this.port,
        hostname: "0.0.0.0",
      });
    } catch (error) {
      logger.error(`启动API服务器失败: ${error.message}`, "ApiServer");
      throw error;
    }
  }

  /**
   * 停止服务器
   */
  async stop(): Promise<void> {
    try {
      this.apiRouter.close();
      logger.info("API服务器已停止", "ApiServer");
    } catch (error) {
      logger.error(`停止API服务器失败: ${error.message}`, "ApiServer");
      throw error;
    }
  }
}
