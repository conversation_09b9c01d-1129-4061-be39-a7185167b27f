import { ProxyNode, TestConfig, TestResult } from "../types/index.ts";
import { logger } from "../utils/logger.ts";

/**
 * 速度测试器
 */
export class SpeedTester {
  private config: TestConfig;

  constructor(config: TestConfig) {
    this.config = config;
  }

  /**
   * 测试节点速度
   */
  async testNodeSpeed(node: ProxyNode): Promise<Partial<TestResult>> {
    try {
      logger.debug(`开始测试节点速度: ${node.name}`, "SpeedTester");

      const result: Partial<TestResult> = {
        nodeId: node.id,
        downloadSpeed: -1,
        uploadSpeed: -1,
      };

      // 测试下载速度
      try {
        result.downloadSpeed = await this.testDownloadSpeed(node);
      } catch (error) {
        logger.warn(`下载速度测试失败: ${node.name}, ${error.message}`, "SpeedTester");
      }

      // 测试上传速度
      try {
        result.uploadSpeed = await this.testUploadSpeed(node);
      } catch (error) {
        logger.warn(`上传速度测试失败: ${node.name}, ${error.message}`, "SpeedTester");
      }

      logger.debug(
        `节点速度测试完成: ${node.name}, 下载: ${result.downloadSpeed}KB/s, 上传: ${result.uploadSpeed}KB/s`,
        "SpeedTester",
      );

      return result;
    } catch (error) {
      logger.error(`节点速度测试失败: ${node.name}, ${error.message}`, "SpeedTester");
      return {
        nodeId: node.id,
        downloadSpeed: -1,
        uploadSpeed: -1,
      };
    }
  }

  /**
   * 测试下载速度
   */
  private async testDownloadSpeed(node: ProxyNode): Promise<number> {
    const testUrl = this.config.testUrl || "http://speedtest.tele2.net/1MB.zip";
    const startTime = performance.now();

    try {
      // 创建超时控制器
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), this.config.timeout * 1000);

      // 发起HTTP请求
      const response = await fetch(testUrl, {
        signal: controller.signal,
        headers: {
          "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      // 读取响应数据
      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error("无法获取响应流");
      }

      let totalBytes = 0;
      let chunks = 0;
      const maxChunks = Math.ceil(this.config.speedTestSize / 64); // 限制测试数据量

      while (chunks < maxChunks) {
        const { done, value } = await reader.read();
        if (done) break;

        totalBytes += value.length;
        chunks++;
      }

      clearTimeout(timeoutId);
      reader.cancel();

      const endTime = performance.now();
      const duration = (endTime - startTime) / 1000; // 转换为秒
      const speedKBps = Math.round((totalBytes / 1024) / duration);

      return speedKBps;
    } catch (error) {
      if (error.name === "AbortError") {
        throw new Error("下载速度测试超时");
      }
      throw error;
    }
  }

  /**
   * 测试上传速度
   */
  private async testUploadSpeed(node: ProxyNode): Promise<number> {
    // 生成测试数据
    const testDataSize = Math.min(this.config.speedTestSize * 1024, 1024 * 1024); // 最大1MB
    const testData = new Uint8Array(testDataSize);
    crypto.getRandomValues(testData);

    const uploadUrl = "https://httpbin.org/post"; // 使用httpbin作为测试上传端点
    const startTime = performance.now();

    try {
      // 创建超时控制器
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), this.config.timeout * 1000);

      // 发起POST请求
      const response = await fetch(uploadUrl, {
        method: "POST",
        body: testData,
        signal: controller.signal,
        headers: {
          "Content-Type": "application/octet-stream",
          "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
        },
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const endTime = performance.now();
      const duration = (endTime - startTime) / 1000; // 转换为秒
      const speedKBps = Math.round((testDataSize / 1024) / duration);

      return speedKBps;
    } catch (error) {
      if (error.name === "AbortError") {
        throw new Error("上传速度测试超时");
      }
      throw error;
    }
  }

  /**
   * 批量测试节点速度
   */
  async testNodesSpeed(nodes: ProxyNode[]): Promise<Map<string, Partial<TestResult>>> {
    logger.info(`开始批量测试 ${nodes.length} 个节点的速度`, "SpeedTester");

    const results = new Map<string, Partial<TestResult>>();
    const semaphore = new Semaphore(Math.min(this.config.concurrency, 3)); // 速度测试并发数较低

    const promises = nodes.map(async (node) => {
      await semaphore.acquire();
      try {
        const result = await this.testNodeSpeed(node);
        results.set(node.id, result);
        return result;
      } finally {
        semaphore.release();
      }
    });

    await Promise.all(promises);

    logger.info(`批量速度测试完成: ${results.size} 个节点`, "SpeedTester");
    return results;
  }
}

/**
 * 信号量实现
 */
class Semaphore {
  private permits: number;
  private waiting: Array<() => void> = [];

  constructor(permits: number) {
    this.permits = permits;
  }

  async acquire(): Promise<void> {
    if (this.permits > 0) {
      this.permits--;
      return;
    }

    return new Promise<void>((resolve) => {
      this.waiting.push(resolve);
    });
  }

  release(): void {
    if (this.waiting.length > 0) {
      const resolve = this.waiting.shift()!;
      resolve();
    } else {
      this.permits++;
    }
  }
}
