import {
  decodeBase64 as stdDecodeBase64,
  encodeBase64 as stdEncodeBase64,
} from "https://deno.land/std@0.224.0/encoding/base64.ts";

/**
 * Base64解码工具
 */
export function decodeBase64(input: string): string {
  try {
    // 移除可能的空白字符
    const cleaned = input.replace(/\s/g, "");

    // 补齐padding
    const padded = cleaned + "=".repeat((4 - (cleaned.length % 4)) % 4);

    const decoded = stdDecodeBase64(padded);
    return new TextDecoder().decode(decoded);
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    throw new Error(`Base64解码失败: ${errorMessage}`);
  }
}

/**
 * Base64编码工具
 */
export function encodeBase64(input: string): string {
  try {
    const encoded = stdEncodeBase64(new TextEncoder().encode(input));
    return encoded;
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    throw new Error(`Base64编码失败: ${errorMessage}`);
  }
}

/**
 * URL安全的Base64解码
 */
export function decodeBase64Url(input: string): string {
  try {
    // 将URL安全字符替换为标准Base64字符
    const standardBase64 = input.replace(/-/g, "+").replace(/_/g, "/");
    return decodeBase64(standardBase64);
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    throw new Error(`URL安全Base64解码失败: ${errorMessage}`);
  }
}

/**
 * URL安全的Base64编码
 */
export function encodeBase64Url(input: string): string {
  try {
    const encoded = encodeBase64(input);
    // 移除padding并替换为URL安全字符
    return encoded.replace(/\+/g, "-").replace(/\//g, "_").replace(/=/g, "");
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    throw new Error(`URL安全Base64编码失败: ${errorMessage}`);
  }
}

/**
 * 生成UUID
 */
export function generateUUID(): string {
  return crypto.randomUUID();
}

/**
 * 生成随机字符串
 */
export function generateRandomString(length: number): string {
  const chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
  let result = "";
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}
