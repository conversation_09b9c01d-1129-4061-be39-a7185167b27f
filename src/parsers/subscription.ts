import { ProxyNode, Subscription } from "../types/index.ts";
import { VmessParser } from "./vmess.ts";
import { VlessParser } from "./vless.ts";
import { TrojanParser } from "./trojan.ts";
import { ShadowsocksParser } from "./shadowsocks.ts";
import { decodeBase64 } from "../utils/crypto.ts";
import { logger } from "../utils/logger.ts";

/**
 * 订阅解析器
 */
export class SubscriptionParser {
  private parsers: Map<string, VmessParser | VlessParser | TrojanParser | ShadowsocksParser>;

  constructor() {
    this.parsers = new Map();
    this.parsers.set("vmess", new VmessParser());
    this.parsers.set("vless", new VlessParser());
    this.parsers.set("trojan", new TrojanParser());
    this.parsers.set("ss", new ShadowsocksParser());
  }

  /**
   * 解析订阅链接
   */
  async parseSubscription(url: string): Promise<ProxyNode[]> {
    try {
      logger.info(`开始解析订阅: ${url}`, "SubscriptionParser");

      const response = await fetch(url, {
        headers: {
          "User-Agent": "clash-verge/v1.3.1",
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const content = await response.text();
      return this.parseSubscriptionContent(content);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logger.error(`解析订阅失败: ${errorMessage}`, "SubscriptionParser", { url });
      throw error;
    }
  }

  /**
   * 解析订阅内容
   */
  parseSubscriptionContent(content: string): ProxyNode[] {
    const nodes: ProxyNode[] = [];

    try {
      // 尝试Base64解码
      let decodedContent: string;
      try {
        decodedContent = decodeBase64(content.trim());
      } catch {
        // 如果解码失败，假设内容已经是明文
        decodedContent = content;
      }

      // 按行分割
      const lines = decodedContent.split(/\r?\n/).filter((line) => line.trim());

      for (const line of lines) {
        const trimmedLine = line.trim();
        if (!trimmedLine) continue;

        const node = this.parseSingleProxy(trimmedLine);
        if (node) {
          nodes.push(node);
        }
      }

      logger.info(`成功解析 ${nodes.length} 个节点`, "SubscriptionParser");
      return nodes;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logger.error(`解析订阅内容失败: ${errorMessage}`, "SubscriptionParser");
      throw error;
    }
  }

  /**
   * 解析单个代理链接
   */
  parseSingleProxy(url: string): ProxyNode | null {
    try {
      // 检测协议类型
      const protocol = this.detectProtocol(url);
      if (!protocol) {
        logger.warn(`未知的协议类型: ${url}`, "SubscriptionParser");
        return null;
      }

      const parser = this.parsers.get(protocol);
      if (!parser) {
        logger.warn(`不支持的协议: ${protocol}`, "SubscriptionParser");
        return null;
      }

      return parser.parse(url);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logger.error(`解析代理链接失败: ${errorMessage}`, "SubscriptionParser", { url });
      return null;
    }
  }

  /**
   * 检测协议类型
   */
  private detectProtocol(url: string): string | null {
    const protocols = ["vmess", "vless", "trojan", "ss", "ssr"];

    for (const protocol of protocols) {
      if (url.startsWith(`${protocol}://`)) {
        return protocol === "ssr" ? "ss" : protocol; // SSR使用SS解析器
      }
    }

    return null;
  }

  /**
   * 批量解析订阅
   */
  async parseMultipleSubscriptions(urls: string[]): Promise<{
    nodes: ProxyNode[];
    errors: Array<{ url: string; error: string }>;
  }> {
    const allNodes: ProxyNode[] = [];
    const errors: Array<{ url: string; error: string }> = [];

    const promises = urls.map(async (url) => {
      try {
        const nodes = await this.parseSubscription(url);
        return { url, nodes, error: null };
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        return { url, nodes: [], error: errorMessage };
      }
    });

    const results = await Promise.all(promises);

    for (const result of results) {
      if (result.error) {
        errors.push({ url: result.url, error: result.error });
      } else {
        allNodes.push(...result.nodes);
      }
    }

    // 去重
    const uniqueNodes = this.deduplicateNodes(allNodes);

    logger.info(
      `批量解析完成: ${uniqueNodes.length} 个节点，${errors.length} 个错误`,
      "SubscriptionParser",
    );

    return { nodes: uniqueNodes, errors };
  }

  /**
   * 节点去重
   */
  private deduplicateNodes(nodes: ProxyNode[]): ProxyNode[] {
    const seen = new Set<string>();
    const uniqueNodes: ProxyNode[] = [];

    for (const node of nodes) {
      const key = `${node.type}_${node.server}_${node.port}_${node.uuid || node.password}`;
      if (!seen.has(key)) {
        seen.add(key);
        uniqueNodes.push(node);
      }
    }

    logger.info(
      `去重前: ${nodes.length} 个节点，去重后: ${uniqueNodes.length} 个节点`,
      "SubscriptionParser",
    );
    return uniqueNodes;
  }
}
