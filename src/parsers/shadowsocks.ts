import { Base<PERSON>arser } from "./base.ts";
import { ProxyNode } from "../types/index.ts";
import { decodeBase64 } from "../utils/crypto.ts";
import { logger } from "../utils/logger.ts";

/**
 * Shadowsocks协议解析器
 */
export class ShadowsocksParser extends BaseParser {
  readonly protocol = "ss";

  parse(url: string): ProxyNode | null {
    try {
      if (!this.validateUrl(url)) {
        logger.warn(`无效的Shadowsocks URL格式: ${url}`, "ShadowsocksParser");
        return null;
      }

      const parsed = new URL(url);
      let method: string;
      let password: string;
      let server: string;
      let port: number;

      // 处理两种格式：
      // 1. ss://method:password@server:port#name
      // 2. ss://base64(method:password)@server:port#name

      if (parsed.username && parsed.password) {
        // 格式1：明文用户名密码
        method = parsed.username;
        password = parsed.password;
        server = parsed.hostname!;
        port = parseInt(parsed.port);
      } else {
        // 格式2：Base64编码
        try {
          const decoded = decodeBase64(parsed.username || "");
          const [methodPass, serverPort] = decoded.split("@");
          const [decodedMethod, decodedPassword] = methodPass.split(":");

          method = decodedMethod;
          password = decodedPassword;

          if (serverPort) {
            const [decodedServer, decodedPort] = serverPort.split(":");
            server = decodedServer;
            port = parseInt(decodedPort);
          } else {
            server = parsed.hostname!;
            port = parseInt(parsed.port);
          }
        } catch {
          logger.warn("无法解析Shadowsocks Base64编码", "ShadowsocksParser");
          return null;
        }
      }

      // 验证必需字段
      if (!method || !password || !server || !port) {
        logger.warn("Shadowsocks URL缺少必需字段", "ShadowsocksParser");
        return null;
      }

      const params = this.parseQueryParams(parsed.search);

      const node: ProxyNode = {
        id: this.generateNodeId(server, port, "ss"),
        name: this.cleanNodeName(decodeURIComponent(parsed.hash?.slice(1) || `SS-${server}`)),
        type: "ss",
        server,
        port: this.parsePort(port.toString(), 8388),
        cipher: method,
        password,
      };

      // 可选参数
      if (params.plugin) {
        node.plugin = params.plugin;
        if (params["plugin-opts"]) {
          node.pluginOpts = this.parsePluginOpts(params["plugin-opts"]);
        }
      }

      // UDP支持
      if (params.udp) {
        node.udp = this.parseBoolean(params.udp, true);
      }

      logger.debug(`成功解析Shadowsocks节点: ${node.name}`, "ShadowsocksParser");
      return node;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logger.error(`解析Shadowsocks URL失败: ${errorMessage}`, "ShadowsocksParser", { url });
      return null;
    }
  }

  /**
   * 解析插件选项
   */
  private parsePluginOpts(optsStr: string): Record<string, string> {
    const opts: Record<string, string> = {};
    const pairs = optsStr.split(";");

    for (const pair of pairs) {
      const [key, value] = pair.split("=");
      if (key && value) {
        opts[key.trim()] = value.trim();
      }
    }

    return opts;
  }

  /**
   * 生成Shadowsocks URL
   */
  generateUrl(node: ProxyNode): string {
    try {
      const userInfo = `${node.cipher}:${node.password}`;
      const base64UserInfo = btoa(userInfo);

      const params = new URLSearchParams();
      if (node.plugin) {
        params.set("plugin", node.plugin);
        if (node.pluginOpts) {
          const optsStr = Object.entries(node.pluginOpts)
            .map(([k, v]) => `${k}=${v}`)
            .join(";");
          params.set("plugin-opts", optsStr);
        }
      }

      if (node.udp !== undefined) {
        params.set("udp", node.udp.toString());
      }

      const url = new URL(`ss://${base64UserInfo}@${node.server}:${node.port}`);
      if (params.toString()) {
        url.search = params.toString();
      }
      url.hash = encodeURIComponent(node.name);

      return url.toString();
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logger.error(`生成Shadowsocks URL失败: ${errorMessage}`, "ShadowsocksParser");
      throw error;
    }
  }
}
