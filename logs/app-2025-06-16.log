2025-06-16T23:17:45.152Z [INFO] [MihomoConfigGenerator] 成功生成Mihomo配置: 4 个代理节点
2025-06-16T23:17:45.152Z [WARN] [MihomoConfigGenerator] 不支持的代理类型: unsupported
2025-06-16T23:17:45.178Z [DEBUG] [VlessParser] 成功解析VLESS节点: 测试节点
2025-06-16T23:17:45.179Z [DEBUG] [TrojanParser] 成功解析Trojan节点: 测试节点
2025-06-16T23:17:45.179Z [WARN] [ShadowsocksParser] 无法解析Shadowsocks Base64编码
2025-06-16T23:17:45.185Z [WARN] [VmessParser] 无效的VMess URL格式: invalid://url
2025-06-16T23:17:45.185Z [WARN] [VlessParser] 无效的VLESS URL格式: invalid://url
2025-06-16T23:17:45.185Z [WARN] [TrojanParser] 无效的Trojan URL格式: invalid://url
2025-06-16T23:17:45.185Z [WARN] [ShadowsocksParser] 无效的Shadowsocks URL格式: invalid://url
2025-06-16T23:17:45.185Z [ERROR] [VmessParser] 解析VMess URL失败: Base64解码失败: Failed to decode base64 {"url":"vmess://invalid-base64"}
2025-06-16T23:17:45.186Z [WARN] [VlessParser] VLESS URL缺少必需字段
2025-06-16T23:19:08.476Z [INFO] [MihomoConfigGenerator] 成功生成Mihomo配置: 4 个代理节点
2025-06-16T23:19:08.477Z [WARN] [MihomoConfigGenerator] 不支持的代理类型: unsupported
2025-06-16T23:19:08.500Z [DEBUG] [VmessParser] 成功解析VMess节点: TestNode
2025-06-16T23:19:08.501Z [DEBUG] [VlessParser] 成功解析VLESS节点: TestNode
2025-06-16T23:19:08.507Z [DEBUG] [TrojanParser] 成功解析Trojan节点: TestNode
2025-06-16T23:19:08.507Z [WARN] [ShadowsocksParser] 无法解析Shadowsocks Base64编码
2025-06-16T23:19:08.508Z [DEBUG] [VmessParser] 成功解析VMess节点: Node1
2025-06-16T23:19:08.508Z [DEBUG] [VlessParser] 成功解析VLESS节点: Node2
2025-06-16T23:19:08.508Z [INFO] [SubscriptionParser] 成功解析 2 个节点
2025-06-16T23:19:08.508Z [WARN] [VmessParser] 无效的VMess URL格式: invalid://url
2025-06-16T23:19:08.508Z [WARN] [VlessParser] 无效的VLESS URL格式: invalid://url
2025-06-16T23:19:08.508Z [WARN] [TrojanParser] 无效的Trojan URL格式: invalid://url
2025-06-16T23:19:08.508Z [WARN] [ShadowsocksParser] 无效的Shadowsocks URL格式: invalid://url
2025-06-16T23:19:08.508Z [ERROR] [VmessParser] 解析VMess URL失败: Base64解码失败: Failed to decode base64 {"url":"vmess://invalid-base64"}
2025-06-16T23:19:08.509Z [WARN] [VlessParser] VLESS URL缺少必需字段
2025-06-16T23:22:53.908Z [INFO] [MihomoConfigGenerator] 成功生成Mihomo配置: 4 个代理节点
2025-06-16T23:22:53.908Z [WARN] [MihomoConfigGenerator] 不支持的代理类型: unsupported
2025-06-16T23:22:53.945Z [DEBUG] [VmessParser] 成功解析VMess节点: TestNode
2025-06-16T23:22:53.945Z [DEBUG] [VlessParser] 成功解析VLESS节点: TestNode
2025-06-16T23:22:53.945Z [DEBUG] [TrojanParser] 成功解析Trojan节点: TestNode
2025-06-16T23:22:53.946Z [DEBUG] [ShadowsocksParser] 成功解析Shadowsocks节点: TestNode
2025-06-16T23:22:53.951Z [DEBUG] [VmessParser] 成功解析VMess节点: Node1
2025-06-16T23:22:53.951Z [DEBUG] [VlessParser] 成功解析VLESS节点: Node2
2025-06-16T23:22:53.951Z [INFO] [SubscriptionParser] 成功解析 2 个节点
2025-06-16T23:22:53.951Z [WARN] [VmessParser] 无效的VMess URL格式: invalid://url
2025-06-16T23:22:53.951Z [WARN] [VlessParser] 无效的VLESS URL格式: invalid://url
2025-06-16T23:22:53.951Z [WARN] [TrojanParser] 无效的Trojan URL格式: invalid://url
2025-06-16T23:22:53.951Z [WARN] [ShadowsocksParser] 无效的Shadowsocks URL格式: invalid://url
2025-06-16T23:22:53.952Z [ERROR] [VmessParser] 解析VMess URL失败: Base64解码失败: Failed to decode base64 {"url":"vmess://invalid-base64"}
2025-06-16T23:22:53.952Z [WARN] [VlessParser] VLESS URL缺少必需字段
2025-06-16T23:24:57.847Z [ERROR] [DatabaseManager] 数据库初始化失败: No such file or directory (os error 2): remove 'data/nodes.db-journal'
2025-06-16T23:24:57.877Z [INFO] [DatabaseManager] 数据库初始化完成: data/nodes.db
2025-06-16T23:24:57.878Z [INFO] [Main] Sub Node Manage 启动中...
2025-06-16T23:24:57.878Z [INFO] [ApiServer] API服务器启动中，端口: 8080
2025-06-16T23:25:07.339Z [INFO] [DatabaseManager] 数据库初始化完成: data/nodes.db
2025-06-16T23:25:07.340Z [INFO] [Main] Sub Node Manage 启动中...
2025-06-16T23:25:07.340Z [INFO] [ApiServer] API服务器启动中，端口: 8080
2025-06-16T23:25:14.732Z [INFO] [DatabaseManager] 数据库初始化完成: data/nodes.db
2025-06-16T23:25:14.734Z [INFO] [Main] Sub Node Manage 启动中...
2025-06-16T23:25:14.734Z [INFO] [ApiServer] API服务器启动中，端口: 8080
2025-06-16T23:26:03.335Z [INFO] [ApiServer] GET /api/health - 200 - 0ms
2025-06-16T23:26:08.555Z [INFO] [ApiServer] GET /api/nodes - 200 - 3ms
2025-06-16T23:26:13.893Z [INFO] [ApiServer] GET /api/subscriptions - 200 - 0ms
2025-06-16T23:26:25.379Z [INFO] [DatabaseManager] 数据库初始化完成: data/nodes.db
2025-06-16T23:26:25.385Z [INFO] [Main] Sub Node Manage 启动中...
2025-06-16T23:26:25.386Z [INFO] [ApiServer] API服务器启动中，端口: 8080
2025-06-16T23:26:34.877Z [INFO] [ApiServer] POST /api/parse - 400 - 0ms
2025-06-16T23:26:58.874Z [INFO] [ApiServer] POST /api/subscriptions/parse - 400 - 0ms
2025-06-16T23:27:19.607Z [INFO] [DatabaseManager] 数据库初始化完成: data/nodes.db
2025-06-16T23:27:19.610Z [INFO] [Main] Sub Node Manage 启动中...
2025-06-16T23:27:19.610Z [INFO] [ApiServer] API服务器启动中，端口: 8080
2025-06-16T23:27:31.065Z [ERROR] [ApiRouter] 解析订阅失败 {}
2025-06-16T23:27:31.067Z [INFO] [ApiServer] POST /api/subscriptions/parse - 200 - 2ms
2025-06-16T23:27:45.715Z [INFO] [DatabaseManager] 数据库初始化完成: data/nodes.db
2025-06-16T23:27:45.716Z [INFO] [Main] Sub Node Manage 启动中...
2025-06-16T23:27:45.717Z [INFO] [ApiServer] API服务器启动中，端口: 8080
2025-06-16T23:27:54.174Z [INFO] [DatabaseManager] 数据库初始化完成: data/nodes.db
2025-06-16T23:27:54.175Z [INFO] [Main] Sub Node Manage 启动中...
2025-06-16T23:27:54.175Z [INFO] [ApiServer] API服务器启动中，端口: 8080
